"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import BlogCard from "./blog-card";
import { useRouter } from "next/navigation";

interface BlogPost {
    id: number;
    date: string;
    title: string;
    image: string;
    excerpt: string;
}

interface BlogCarouselProps {
    posts: BlogPost[];
}

const BlogCarousel = ({ posts }: BlogCarouselProps) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [cardsToShow, setCardsToShow] = useState(3);
    const [cardWidth, setCardWidth] = useState(320);
    const [containerWidth, setContainerWidth] = useState(960);
    const router = useRouter();

    // Handle responsive card sizing with viewport-aware calculations
    useEffect(() => {
        const handleResize = () => {
            const viewportWidth = window.innerWidth;

            // Calculate container margins based on screen size
            const getContainerMargins = (width: number) => {
                if (width < 640) return 16; // 8px each side
                if (width < 1024) return 32; // 16px each side
                if (width < 1280) return 64; // 32px each side
                return 128; // 64px each side
            };

            const containerMargins = getContainerMargins(viewportWidth);
            const buttonSpace = 80; // Space for navigation buttons
            const availableWidth =
                viewportWidth - containerMargins - buttonSpace;

            // Determine cards to show based on viewport
            let cardsToShow: number;
            if (viewportWidth < 640) {
                cardsToShow = 1;
            } else if (viewportWidth < 1024) {
                cardsToShow = 2;
            } else {
                cardsToShow = 3;
            }

            // Calculate optimal card width
            const cardGap = 16;
            const totalGaps = (cardsToShow - 1) * cardGap;
            const calculatedCardWidth = Math.floor(
                (availableWidth - totalGaps) / cardsToShow,
            );

            // Apply constraints while maintaining responsiveness
            const minCardWidth = 240;
            const maxCardWidth = 420;
            const finalCardWidth = Math.max(
                minCardWidth,
                Math.min(maxCardWidth, calculatedCardWidth),
            );

            setCardsToShow(cardsToShow);
            setCardWidth(finalCardWidth);

            // Set container width to prevent overflow
            const calculatedContainerWidth = Math.min(
                cardsToShow * finalCardWidth + (cardsToShow - 1) * 16,
                viewportWidth - 160,
            );
            setContainerWidth(calculatedContainerWidth);
        };

        handleResize();
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    // Calculate max index
    const maxIndex = Math.max(0, posts.length - cardsToShow);

    const nextSlide = () => {
        if (currentIndex < maxIndex) {
            setCurrentIndex(prev => prev + 1);
        } else {
            // Loop back to beginning for infinite scroll
            setCurrentIndex(0);
        }
    };

    const prevSlide = () => {
        if (currentIndex > 0) {
            setCurrentIndex(prev => prev - 1);
        } else {
            // Loop to end for infinite scroll
            setCurrentIndex(maxIndex);
        }
    };

    const handlePostClick = (postId: string) => {
        router.push(`/blog/${postId}`);
    };

    if (posts.length === 0) {
        return (
            <div className="text-center py-12">
                <p className="text-gray-500 text-lg">No blog posts available</p>
            </div>
        );
    }

    return (
        <div className="relative w-full max-w-full px-2 sm:px-4 md:px-8 lg:px-16">
            {/* Visible area with dynamic width based on cards shown */}
            <div
                className="overflow-hidden mx-auto"
                style={{
                    width: `${Math.min(
                        cardsToShow * cardWidth + (cardsToShow - 1) * 16,
                        window.innerWidth - 160,
                    )}px`, // Dynamic width with viewport constraint
                    maxWidth: "100%",
                }}
            >
                {/* Navigation Buttons */}
                {posts.length > cardsToShow && (
                    <>
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={prevSlide}
                            className="absolute -left-4 sm:-left-6 md:-left-8 lg:-left-10 top-1/2 -translate-y-1/2 z-10 bg-gray-100/80 hover:bg-gray-200/80 shadow-lg rounded-full w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 transition-all duration-300 text-gray-500 hover:text-gray-600"
                        >
                            <ChevronLeft className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5" />
                        </Button>

                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={nextSlide}
                            className="absolute -right-4 sm:-right-6 md:-right-8 lg:-right-10 top-1/2 -translate-y-1/2 z-10 bg-gray-100/80 hover:bg-gray-200/80 shadow-lg rounded-full w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 transition-all duration-300 text-gray-500 hover:text-gray-600"
                        >
                            <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5" />
                        </Button>
                    </>
                )}

                {/* Blog Posts Cards Container with Smooth Sliding */}
                <div className="overflow-hidden">
                    <motion.div
                        className="flex gap-4"
                        style={{
                            width: `${posts.length * (cardWidth + 16)}px`, // cardWidth + gap
                            transform: `translateX(-${
                                currentIndex * (cardWidth + 16)
                            }px)`,
                        }}
                        animate={{
                            transform: `translateX(-${
                                currentIndex * (cardWidth + 16)
                            }px)`,
                        }}
                        transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 30,
                            duration: 0.8,
                        }}
                    >
                        {posts.map((post, index) => (
                            <BlogCard
                                key={post.id}
                                post={post}
                                index={index}
                                onClick={handlePostClick}
                                style={{ width: `${cardWidth}px` }}
                            />
                        ))}
                    </motion.div>
                </div>
            </div>
        </div>
    );
};

export default BlogCarousel;
